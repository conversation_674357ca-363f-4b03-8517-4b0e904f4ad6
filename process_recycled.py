#!/usr/bin/env python3
"""
Script to process output.csv and create recycled.csv with RCL-SB rows
where click_id is populated from the first ACT-SB occurrence of the same MSISDN.
"""

import csv
import sys
from collections import defaultdict

def process_csv():
    # Dictionary to store the first ACT-SB click_id for each MSISDN
    msisdn_to_click_id = {}
    
    # List to store RCL-SB rows
    rcl_sb_rows = []
    
    print("Reading output.csv and processing data...")
    
    # First pass: collect ACT-SB click_ids and RCL-SB rows
    with open('output.csv', 'r', newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        
        for row_num, row in enumerate(reader, start=2):  # start=2 because header is line 1
            if row_num % 10000 == 0:
                print(f"Processed {row_num} rows...")
            
            status = row['STATUS']
            msisdn = row['MSISDN']
            click_id = row['click_id']
            
            if status == 'ACT-SB':
                # Store the first ACT-SB click_id for this MSISDN (if we haven't seen it before and click_id is not NULL)
                if msisdn not in msisdn_to_click_id and click_id and click_id.strip() != 'NULL':
                    msisdn_to_click_id[msisdn] = click_id
            
            elif status == 'RCL-SB':
                # Store RCL-SB row for later processing
                rcl_sb_rows.append(row)
    
    print(f"Found {len(rcl_sb_rows)} RCL-SB rows")
    print(f"Found {len(msisdn_to_click_id)} unique MSISDNs with ACT-SB status")
    
    # Second pass: update RCL-SB rows with corresponding ACT-SB click_ids
    updated_rows = []
    matched_count = 0
    unmatched_count = 0
    
    print("Updating RCL-SB rows with ACT-SB click_ids...")
    
    for row in rcl_sb_rows:
        msisdn = row['MSISDN']
        
        if msisdn in msisdn_to_click_id:
            # Update click_id with the one from ACT-SB
            row['click_id'] = msisdn_to_click_id[msisdn]
            matched_count += 1
        else:
            # Keep original click_id (which should be NULL)
            unmatched_count += 1
        
        updated_rows.append(row)
    
    print(f"Matched {matched_count} RCL-SB rows with ACT-SB click_ids")
    print(f"Unmatched {unmatched_count} RCL-SB rows (no corresponding ACT-SB found)")
    
    # Write the recycled.csv file
    print("Writing recycled.csv...")
    
    with open('recycled.csv', 'w', newline='', encoding='utf-8') as csvfile:
        if updated_rows:
            fieldnames = updated_rows[0].keys()
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            writer.writerows(updated_rows)
    
    print(f"Successfully created recycled.csv with {len(updated_rows)} rows")
    
    # Print some statistics
    print("\nStatistics:")
    print(f"- Total RCL-SB rows: {len(rcl_sb_rows)}")
    print(f"- Rows with matched click_id: {matched_count}")
    print(f"- Rows without matched click_id: {unmatched_count}")
    print(f"- Match rate: {matched_count/len(rcl_sb_rows)*100:.2f}%")

if __name__ == "__main__":
    try:
        process_csv()
    except FileNotFoundError:
        print("Error: output.csv file not found in the current directory")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
